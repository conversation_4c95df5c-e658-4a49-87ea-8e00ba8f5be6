---
description: Explore in-depth reference for utility functions in Ultralytics data module. Learn about image verification, dataset handling, and more.
keywords: Ultralytics, dataset utils, data handling, image verification, Python, data module
---

# Reference for `ultralytics/data/utils.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/data/utils.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/data/utils.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/data/utils.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.data.utils.HUBDatasetStats

<br><br><hr><br>

## ::: ultralytics.data.utils.img2label_paths

<br><br><hr><br>

## ::: ultralytics.data.utils.get_hash

<br><br><hr><br>

## ::: ultralytics.data.utils.exif_size

<br><br><hr><br>

## ::: ultralytics.data.utils.verify_image

<br><br><hr><br>

## ::: ultralytics.data.utils.verify_image_label

<br><br><hr><br>

## ::: ultralytics.data.utils.polygon2mask

<br><br><hr><br>

## ::: ultralytics.data.utils.polygons2masks

<br><br><hr><br>

## ::: ultralytics.data.utils.polygons2masks_overlap

<br><br><hr><br>

## ::: ultralytics.data.utils.find_dataset_yaml

<br><br><hr><br>

## ::: ultralytics.data.utils.check_det_dataset

<br><br><hr><br>

## ::: ultralytics.data.utils.check_cls_dataset

<br><br><hr><br>

## ::: ultralytics.data.utils.compress_one_image

<br><br><hr><br>

## ::: ultralytics.data.utils.autosplit

<br><br><hr><br>

## ::: ultralytics.data.utils.load_dataset_cache_file

<br><br><hr><br>

## ::: ultralytics.data.utils.save_dataset_cache_file

<br><br>
