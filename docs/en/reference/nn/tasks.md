---
description: Dive into the intricacies of YOLO tasks.py. Learn about DetectionModel, PoseModel and more for powerful AI development.
keywords: Ultralytics, YOLO, nn tasks, DetectionModel, PoseModel, RTDETRDetectionModel, model weights, parse model, AI development
---

# Reference for `ultralytics/nn/tasks.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/nn/tasks.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/nn/tasks.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/nn/tasks.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.nn.tasks.BaseModel

<br><br><hr><br>

## ::: ultralytics.nn.tasks.DetectionModel

<br><br><hr><br>

## ::: ultralytics.nn.tasks.OBBModel

<br><br><hr><br>

## ::: ultralytics.nn.tasks.SegmentationModel

<br><br><hr><br>

## ::: ultralytics.nn.tasks.PoseModel

<br><br><hr><br>

## ::: ultralytics.nn.tasks.ClassificationModel

<br><br><hr><br>

## ::: ultralytics.nn.tasks.RTDETRDetectionModel

<br><br><hr><br>

## ::: ultralytics.nn.tasks.WorldModel

<br><br><hr><br>

## ::: ultralytics.nn.tasks.Ensemble

<br><br><hr><br>

## ::: ultralytics.nn.tasks.SafeClass

<br><br><hr><br>

## ::: ultralytics.nn.tasks.SafeUnpickler

<br><br><hr><br>

## ::: ultralytics.nn.tasks.temporary_modules

<br><br><hr><br>

## ::: ultralytics.nn.tasks.torch_safe_load

<br><br><hr><br>

## ::: ultralytics.nn.tasks.attempt_load_weights

<br><br><hr><br>

## ::: ultralytics.nn.tasks.attempt_load_one_weight

<br><br><hr><br>

## ::: ultralytics.nn.tasks.parse_model

<br><br><hr><br>

## ::: ultralytics.nn.tasks.yaml_model_load

<br><br><hr><br>

## ::: ultralytics.nn.tasks.guess_model_scale

<br><br><hr><br>

## ::: ultralytics.nn.tasks.guess_model_task

<br><br>
