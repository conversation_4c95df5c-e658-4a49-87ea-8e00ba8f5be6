---
description: Explore utility functions for Ultralytics YOLO such as checking versions, image sizes, and requirements.
keywords: Ultralytics, YOLO, utility functions, version checks, requirements, image size
---

# Reference for `ultralytics/utils/checks.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/checks.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/checks.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/checks.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.utils.checks.parse_requirements

<br><br><hr><br>

## ::: ultralytics.utils.checks.parse_version

<br><br><hr><br>

## ::: ultralytics.utils.checks.is_ascii

<br><br><hr><br>

## ::: ultralytics.utils.checks.check_imgsz

<br><br><hr><br>

## ::: ultralytics.utils.checks.check_version

<br><br><hr><br>

## ::: ultralytics.utils.checks.check_latest_pypi_version

<br><br><hr><br>

## ::: ultralytics.utils.checks.check_pip_update_available

<br><br><hr><br>

## ::: ultralytics.utils.checks.check_font

<br><br><hr><br>

## ::: ultralytics.utils.checks.check_python

<br><br><hr><br>

## ::: ultralytics.utils.checks.check_requirements

<br><br><hr><br>

## ::: ultralytics.utils.checks.check_torchvision

<br><br><hr><br>

## ::: ultralytics.utils.checks.check_suffix

<br><br><hr><br>

## ::: ultralytics.utils.checks.check_yolov5u_filename

<br><br><hr><br>

## ::: ultralytics.utils.checks.check_model_file_from_stem

<br><br><hr><br>

## ::: ultralytics.utils.checks.check_file

<br><br><hr><br>

## ::: ultralytics.utils.checks.check_yaml

<br><br><hr><br>

## ::: ultralytics.utils.checks.check_is_path_safe

<br><br><hr><br>

## ::: ultralytics.utils.checks.check_imshow

<br><br><hr><br>

## ::: ultralytics.utils.checks.check_yolo

<br><br><hr><br>

## ::: ultralytics.utils.checks.collect_system_info

<br><br><hr><br>

## ::: ultralytics.utils.checks.check_amp

<br><br><hr><br>

## ::: ultralytics.utils.checks.git_describe

<br><br><hr><br>

## ::: ultralytics.utils.checks.print_args

<br><br><hr><br>

## ::: ultralytics.utils.checks.cuda_device_count

<br><br><hr><br>

## ::: ultralytics.utils.checks.cuda_is_available

<br><br>
